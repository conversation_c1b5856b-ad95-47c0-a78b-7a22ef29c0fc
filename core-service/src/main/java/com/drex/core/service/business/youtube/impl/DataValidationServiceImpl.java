package com.drex.core.service.business.youtube.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.request.RexyReportRequest;
import com.drex.core.model.youtube.YouTubeAntiCheatConstant;
import com.drex.core.service.business.youtube.DataValidationService;
import com.drex.core.service.cache.model.SessionEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据验证服务实现
 */
@Slf4j
@Service
public class DataValidationServiceImpl implements DataValidationService {

    private static final long MAX_TIME_DIFFERENCE_MS = 5000; // 5秒
    private static final String[] VALID_EVENT_TYPES = {
            "PLAY", "PAUSE", "SEEKING", "SEEKED", "TIMEUPDATE", "ENDED",
            "FOCUS", "BLUR", "VISIBILITY_CHANGE", "PLAY<PERSON>CK_RATE_CHANGE"
    };

    @Override
    public ValidationResult validateReportRequest(RexyReportRequest request) {
        try {
            // 1. 基础字段验证
            if (request.getSessionId() == null || request.getSessionId().trim().isEmpty()) {
                return new ValidationResult(false, "Session ID is required", "MISSING_SESSION_ID");
            }

            if (request.getCustomerId() == null || request.getCustomerId().trim().isEmpty()) {
                return new ValidationResult(false, "Customer ID is required", "MISSING_CUSTOMER_ID");
            }

            if (request.getEncryptedData() == null || request.getEncryptedData().trim().isEmpty()) {
                return new ValidationResult(false, "Encrypted data is required", "MISSING_ENCRYPTED_DATA");
            }

            if (request.getSignature() == null || request.getSignature().trim().isEmpty()) {
                return new ValidationResult(false, "Signature is required", "MISSING_SIGNATURE");
            }

            // 2. 时间戳验证
            if (request.getClientTimestamp() == null) {
                return new ValidationResult(false, "Client timestamp is required", "MISSING_TIMESTAMP");
            }

            long serverTimestamp = System.currentTimeMillis();
            long timeDifference = Math.abs(serverTimestamp - request.getClientTimestamp());
            if (timeDifference > MAX_TIME_DIFFERENCE_MS) {
                return new ValidationResult(false, "Client timestamp is too far from server time", 
                        "TIMESTAMP_ANOMALY");
            }

            return new ValidationResult(true, null, null);

        } catch (Exception e) {
            log.error("Failed to validate report request", e);
            return new ValidationResult(false, "Validation error occurred", "VALIDATION_ERROR");
        }
    }

    @Override
    public List<SessionEvent> validateAndDecryptEvents(String encryptedData, String signature, String secretKey) {
        try {
            // 1. 验证签名
            if (!verifySignature(encryptedData, signature, secretKey)) {
                log.warn("Invalid signature detected");
                return new ArrayList<>();
            }

            // 2. 解密数据
            String decryptedData = decryptData(encryptedData, secretKey);
            if (decryptedData == null) {
                log.warn("Failed to decrypt data");
                return new ArrayList<>();
            }

            // 3. 解析事件数据
            List<SessionEvent> events = parseEvents(decryptedData);
            
            // 4. 验证事件格式
            return events.stream()
                    .filter(event -> validateEventFormat(event).isValid())
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Failed to validate and decrypt events", e);
            return new ArrayList<>();
        }
    }

    @Override
    public TimestampValidationResult validateTimestamps(List<SessionEvent> events, Long serverTimestamp) {
        List<String> anomalousEventIds = new ArrayList<>();
        double maxTimeDifference = 0.0;
        int anomalousCount = 0;

        for (SessionEvent event : events) {
            if (event.getClientTimestamp() != null && event.getServerTimestamp() != null) {
                double timeDiff = Math.abs(event.getServerTimestamp() - event.getClientTimestamp());
                maxTimeDifference = Math.max(maxTimeDifference, timeDiff);

                if (timeDiff > MAX_TIME_DIFFERENCE_MS) {
                    anomalousEventIds.add(event.getId());
                    anomalousCount++;
                }
            }
        }

        boolean isValid = anomalousCount == 0;
        return new TimestampValidationResult(isValid, maxTimeDifference, anomalousCount, anomalousEventIds);
    }

    @Override
    public EventSequenceValidationResult validateEventSequence(List<SessionEvent> events) {
        List<String> invalidTransitions = new ArrayList<>();
        int sequenceErrors = 0;

        // 按时间戳排序事件
        List<SessionEvent> sortedEvents = events.stream()
                .sorted(Comparator.comparing(SessionEvent::getClientTimestamp))
                .collect(Collectors.toList());

        String previousEventType = null;
        for (SessionEvent event : sortedEvents) {
            String currentEventType = event.getEventType();
            
            if (previousEventType != null) {
                if (!isValidTransition(previousEventType, currentEventType)) {
                    invalidTransitions.add(previousEventType + " -> " + currentEventType);
                    sequenceErrors++;
                }
            }
            
            previousEventType = currentEventType;
        }

        boolean isValid = sequenceErrors == 0;
        return new EventSequenceValidationResult(isValid, sequenceErrors, invalidTransitions);
    }

    @Override
    public DuplicateEventDetectionResult detectDuplicateEvents(List<SessionEvent> events) {
        Map<String, Integer> eventSignatures = new HashMap<>();
        List<String> duplicateEventIds = new ArrayList<>();
        int duplicateCount = 0;

        for (SessionEvent event : events) {
            String signature = generateEventSignature(event);
            int count = eventSignatures.getOrDefault(signature, 0) + 1;
            eventSignatures.put(signature, count);

            if (count > 1) {
                duplicateEventIds.add(event.getId());
                duplicateCount++;
            }
        }

        boolean hasDuplicates = duplicateCount > 0;
        DuplicateEventDetectionResult result = new DuplicateEventDetectionResult(
                hasDuplicates, duplicateCount, duplicateEventIds);
        result.setDuplicateGroups(eventSignatures.entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

        return result;
    }

    @Override
    public FormatValidationResult validateEventFormat(SessionEvent event) {
        List<String> formatErrors = new ArrayList<>();

        // 验证必填字段
        if (event.getId() == null || event.getId().trim().isEmpty()) {
            formatErrors.add("Event ID is required");
        }

        if (event.getEventType() == null || event.getEventType().trim().isEmpty()) {
            formatErrors.add("Event type is required");
        } else if (!isValidEventType(event.getEventType())) {
            formatErrors.add("Invalid event type: " + event.getEventType());
        }

        if (event.getClientTimestamp() == null) {
            formatErrors.add("Client timestamp is required");
        }

        if (event.getSessionId() == null || event.getSessionId().trim().isEmpty()) {
            formatErrors.add("Session ID is required");
        }

        if (event.getCustomerId() == null || event.getCustomerId().trim().isEmpty()) {
            formatErrors.add("Customer ID is required");
        }

        // 验证事件数据格式
        if (event.getEventData() != null) {
            validateEventDataFormat(event.getEventType(), event.getEventData(), formatErrors);
        }

        boolean isValid = formatErrors.isEmpty();
        return new FormatValidationResult(isValid, formatErrors);
    }

    @Override
    public Map<String, Double> calculateFraudIndicators(List<SessionEvent> events, ValidationResult validationResults) {
        Map<String, Double> indicators = new HashMap<>();

        try {
            // 1. 时间戳异常指标
            TimestampValidationResult timestampResult = validateTimestamps(events, System.currentTimeMillis());
            double timestampAnomalyScore = calculateTimestampAnomalyScore(timestampResult, events.size());
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.TIMESTAMP_ANOMALY.getCode(), 
                    timestampAnomalyScore);

            // 2. 事件序列异常指标
            EventSequenceValidationResult sequenceResult = validateEventSequence(events);
            double sequenceAnomalyScore = calculateSequenceAnomalyScore(sequenceResult, events.size());
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.EVENT_SEQUENCE_ANOMALY.getCode(), 
                    sequenceAnomalyScore);

            // 3. 重复事件指标
            DuplicateEventDetectionResult duplicateResult = detectDuplicateEvents(events);
            double duplicateEventScore = calculateDuplicateEventScore(duplicateResult, events.size());
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.DUPLICATE_EVENT.getCode(), 
                    duplicateEventScore);

            // 4. 数据格式异常指标
            double formatErrorScore = calculateFormatErrorScore(events);
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.INVALID_DATA_FORMAT.getCode(), 
                    formatErrorScore);

        } catch (Exception e) {
            log.error("Failed to calculate fraud indicators", e);
            // 返回高风险指标
            indicators.put(YouTubeAntiCheatConstant.FraudIndicatorType.TIMESTAMP_ANOMALY.getCode(), 1.0);
        }

        return indicators;
    }

    /**
     * 验证签名
     */
    private boolean verifySignature(String data, String signature, String secretKey) {
        try {
            Mac mac = Mac.getInstance(YouTubeAntiCheatConstant.DEFAULT_SIGNATURE_ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), 
                    YouTubeAntiCheatConstant.DEFAULT_SIGNATURE_ALGORITHM);
            mac.init(keySpec);
            
            byte[] expectedSignature = mac.doFinal(data.getBytes());
            String expectedSignatureHex = bytesToHex(expectedSignature);
            
            return expectedSignatureHex.equals(signature);
        } catch (Exception e) {
            log.error("Failed to verify signature", e);
            return false;
        }
    }

    /**
     * 解密数据
     */
    private String decryptData(String encryptedData, String secretKey) {
        try {
            // TODO: 实现具体的解密逻辑
            // 这里应该使用AES等加密算法进行解密
            // 当前返回原始数据作为临时实现
            return encryptedData;
        } catch (Exception e) {
            log.error("Failed to decrypt data", e);
            return null;
        }
    }

    /**
     * 解析事件数据
     */
    private List<SessionEvent> parseEvents(String decryptedData) {
        try {
            // 假设数据是JSON格式的事件数组
            return JSON.parseArray(decryptedData, SessionEvent.class);
        } catch (Exception e) {
            log.error("Failed to parse events", e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查事件类型是否有效
     */
    private boolean isValidEventType(String eventType) {
        return Arrays.asList(VALID_EVENT_TYPES).contains(eventType);
    }

    /**
     * 检查事件转换是否有效
     */
    private boolean isValidTransition(String fromEvent, String toEvent) {
        // TODO: 实现具体的事件转换规则
        // 例如：PAUSE后不能直接是ENDED，必须先PLAY
        return true; // 临时返回true
    }

    /**
     * 生成事件签名用于重复检测
     */
    private String generateEventSignature(SessionEvent event) {
        return String.format("%s_%s_%s_%d", 
                event.getEventType(), 
                event.getSessionId(), 
                event.getCustomerId(),
                event.getClientTimestamp() / 1000); // 精确到秒
    }

    /**
     * 验证事件数据格式
     */
    private void validateEventDataFormat(String eventType, Map<String, Object> eventData, 
                                       List<String> formatErrors) {
        // TODO: 根据不同的事件类型验证数据格式
        // 例如：TIMEUPDATE事件应该包含currentTime字段
    }

    /**
     * 计算时间戳异常分数
     */
    private double calculateTimestampAnomalyScore(TimestampValidationResult result, int totalEvents) {
        if (totalEvents == 0) return 0.0;
        return (double) result.getAnomalousEventCount() / totalEvents;
    }

    /**
     * 计算序列异常分数
     */
    private double calculateSequenceAnomalyScore(EventSequenceValidationResult result, int totalEvents) {
        if (totalEvents == 0) return 0.0;
        return Math.min(1.0, (double) result.getSequenceErrors() / totalEvents);
    }

    /**
     * 计算重复事件分数
     */
    private double calculateDuplicateEventScore(DuplicateEventDetectionResult result, int totalEvents) {
        if (totalEvents == 0) return 0.0;
        return Math.min(1.0, (double) result.getDuplicateCount() / totalEvents);
    }

    /**
     * 计算格式错误分数
     */
    private double calculateFormatErrorScore(List<SessionEvent> events) {
        int errorCount = 0;
        for (SessionEvent event : events) {
            FormatValidationResult result = validateEventFormat(event);
            if (!result.isValid()) {
                errorCount++;
            }
        }
        return events.isEmpty() ? 0.0 : Math.min(1.0, (double) errorCount / events.size());
    }

    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }
}
