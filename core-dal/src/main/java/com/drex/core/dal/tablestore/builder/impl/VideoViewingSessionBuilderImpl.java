package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.VideoViewingSessionBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.VideoViewingSession;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Repository("videoViewingSessionBuilder")
public class VideoViewingSessionBuilderImpl extends WideColumnStoreBuilder<VideoViewingSession> implements VideoViewingSessionBuilder {

    @Autowired
    private IdUtils idUtils;

    public String getTableName() {
        return Constant.TABLE_NAME_VIDEO_VIEWING_SESSION;
    }

    @PostConstruct
    public void init() {
        super.init(VideoViewingSession.class);
    }

    @Override
    public VideoViewingSession getBySessionId(String sessionId) {
        VideoViewingSession entity = new VideoViewingSession();
        entity.setSessionId(sessionId);
        return getRow(entity);
    }

    @Override
    public List<VideoViewingSession> getByCustomerIdAndVideoId(String customerId, String videoId) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_CUSTOMER_ID, customerId),
                QueryBuilders.term(Constant.COLUMN_NAME_VIDEO_ID, videoId)
        ));

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_CREATED, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_VIDEO_VIEWING_SESSION, boolQuery, sort);
    }

    @Override
    public List<VideoViewingSession> getByCustomerId(String customerId) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_CUSTOMER_ID, customerId)
        ));

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_CREATED, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_VIDEO_VIEWING_SESSION, boolQuery, sort);
    }

    @Override
    public List<VideoViewingSession> getByStatus(String status) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_SESSION_STATUS, status)
        ));

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_CREATED, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_VIDEO_VIEWING_SESSION, boolQuery, sort);
    }

    @Override
    public Boolean insert(VideoViewingSession session) {
        if (session.getSessionId() == null) {
            session.setSessionId(idUtils.nextId());
        }
        if (session.getCreated() == null) {
            session.setCreated(System.currentTimeMillis());
        }
        session.setModified(System.currentTimeMillis());
        return super.putRow(session);
    }

    @Override
    public Boolean update(VideoViewingSession session) {
        session.setModified(System.currentTimeMillis());
        return super.putRow(session);
    }

    @Override
    public Boolean delete(String sessionId) {
        VideoViewingSession entity = new VideoViewingSession();
        entity.setSessionId(sessionId);
        return super.deleteRow(entity);
    }

    @Override
    public Boolean hasRewardGranted(String customerId, String videoId) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_CUSTOMER_ID, customerId),
                QueryBuilders.term(Constant.COLUMN_NAME_VIDEO_ID, videoId),
                QueryBuilders.term(Constant.COLUMN_NAME_REWARD_GRANTED, true)
        ));

        List<VideoViewingSession> sessions = searchAll(Constant.SEARCH_INDEX_VIDEO_VIEWING_SESSION, boolQuery, null);
        return !sessions.isEmpty();
    }
}
