package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.query.RangeQuery;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.UserFingerprintBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.UserFingerprint;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.List;

@Repository("userFingerprintBuilder")
public class UserFingerprintBuilderImpl extends WideColumnStoreBuilder<UserFingerprint> implements UserFingerprintBuilder {

    @Autowired
    private IdUtils idUtils;

    public String getTableName() {
        return Constant.TABLE_NAME_USER_FINGERPRINT;
    }

    @PostConstruct
    public void init() {
        super.init(UserFingerprint.class);
    }

    @Override
    public UserFingerprint getById(String id) {
        UserFingerprint entity = new UserFingerprint();
        entity.setId(id);
        return getRow(entity);
    }

    @Override
    public UserFingerprint getByFingerprintHash(String fingerprintHash) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_FINGERPRINT_HASH, fingerprintHash)
        ));

        List<UserFingerprint> results = searchAll(Constant.SEARCH_INDEX_USER_FINGERPRINT, boolQuery, null);
        return results.isEmpty() ? null : results.get(0);
    }

    @Override
    public List<UserFingerprint> getByCustomerId(String customerId) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_CUSTOMER_ID, customerId)
        ));

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_FIRST_SEEN_AT, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_USER_FINGERPRINT, boolQuery, sort);
    }

    @Override
    public List<UserFingerprint> getByIpAddress(String ipAddress) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_IP_ADDRESS, ipAddress)
        ));

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_FIRST_SEEN_AT, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_USER_FINGERPRINT, boolQuery, sort);
    }

    @Override
    public List<UserFingerprint> getSuspiciousFingerprints() {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_IS_SUSPICIOUS, true)
        ));

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_LAST_SEEN_AT, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_USER_FINGERPRINT, boolQuery, sort);
    }

    @Override
    public Boolean insert(UserFingerprint fingerprint) {
        if (fingerprint.getId() == null) {
            fingerprint.setId(idUtils.nextId());
        }
        if (fingerprint.getCreated() == null) {
            fingerprint.setCreated(System.currentTimeMillis());
        }
        if (fingerprint.getFirstSeenAt() == null) {
            fingerprint.setFirstSeenAt(System.currentTimeMillis());
        }
        fingerprint.setLastSeenAt(System.currentTimeMillis());
        fingerprint.setModified(System.currentTimeMillis());
        return super.putRow(fingerprint);
    }

    @Override
    public Boolean update(UserFingerprint fingerprint) {
        fingerprint.setLastSeenAt(System.currentTimeMillis());
        fingerprint.setModified(System.currentTimeMillis());
        return super.putRow(fingerprint);
    }

    @Override
    public Boolean delete(String id) {
        UserFingerprint entity = new UserFingerprint();
        entity.setId(id);
        return super.deleteRow(entity);
    }

    @Override
    public Boolean isAssociatedWithMultipleUsers(String fingerprintHash, int threshold) {
        UserFingerprint fingerprint = getByFingerprintHash(fingerprintHash);
        if (fingerprint == null || fingerprint.getAssociatedUserIds() == null) {
            return false;
        }
        
        // 假设associatedUserIds是以逗号分隔的字符串
        String[] userIds = fingerprint.getAssociatedUserIds().split(",");
        return userIds.length > threshold;
    }

    @Override
    public List<UserFingerprint> getByTimeRange(Long startTime, Long endTime) {
        RangeQuery rangeQuery = new RangeQuery();
        rangeQuery.setFieldName(Constant.COLUMN_NAME_FIRST_SEEN_AT);
        rangeQuery.setFrom(startTime, true);
        rangeQuery.setTo(endTime, true);

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_FIRST_SEEN_AT, SortOrder.ASC)));

        return searchAll(Constant.SEARCH_INDEX_USER_FINGERPRINT, rangeQuery, sort);
    }
}
