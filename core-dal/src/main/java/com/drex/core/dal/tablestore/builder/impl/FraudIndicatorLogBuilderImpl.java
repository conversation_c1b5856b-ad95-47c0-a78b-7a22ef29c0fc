package com.drex.core.dal.tablestore.builder.impl;

import com.alicloud.openservices.tablestore.model.RowExistenceExpectation;
import com.alicloud.openservices.tablestore.model.search.query.BoolQuery;
import com.alicloud.openservices.tablestore.model.search.query.QueryBuilders;
import com.alicloud.openservices.tablestore.model.search.query.RangeQuery;
import com.alicloud.openservices.tablestore.model.search.sort.FieldSort;
import com.alicloud.openservices.tablestore.model.search.sort.Sort;
import com.alicloud.openservices.tablestore.model.search.sort.SortOrder;
import com.drex.core.dal.tablestore.IdUtils;
import com.drex.core.dal.tablestore.builder.FraudIndicatorLogBuilder;
import com.drex.core.dal.tablestore.constant.Constant;
import com.drex.core.dal.tablestore.model.FraudIndicatorLog;
import com.kikitrade.framework.ots.WideColumnStoreBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.List;

@Repository("fraudIndicatorLogBuilder")
public class FraudIndicatorLogBuilderImpl extends WideColumnStoreBuilder<FraudIndicatorLog> implements FraudIndicatorLogBuilder {

    @Autowired
    private IdUtils idUtils;

    public String getTableName() {
        return Constant.TABLE_NAME_FRAUD_INDICATOR_LOG;
    }

    @PostConstruct
    public void init() {
        super.init(FraudIndicatorLog.class);
    }

    @Override
    public FraudIndicatorLog getById(String id) {
        FraudIndicatorLog entity = new FraudIndicatorLog();
        entity.setId(id);
        return getRow(entity);
    }

    @Override
    public List<FraudIndicatorLog> getBySessionId(String sessionId) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_SESSION_ID, sessionId).build()
        ));

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_TIMESTAMP, SortOrder.ASC)));

        return searchAll(Constant.SEARCH_INDEX_FRAUD_INDICATOR_LOG, boolQuery, sort);
    }

    @Override
    public List<FraudIndicatorLog> getByCustomerId(String customerId) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_CUSTOMER_ID, customerId)
        ));

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_TIMESTAMP, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_FRAUD_INDICATOR_LOG, boolQuery, sort);
    }

    @Override
    public List<FraudIndicatorLog> getByIndicatorType(String indicatorType) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_INDICATOR_TYPE, indicatorType)
        ));

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_TIMESTAMP, SortOrder.DESC)));

        return searchAll(Constant.SEARCH_INDEX_FRAUD_INDICATOR_LOG, boolQuery, sort);
    }

    @Override
    public List<FraudIndicatorLog> getBySessionIdAndIndicatorType(String sessionId, String indicatorType) {
        BoolQuery boolQuery = new BoolQuery();
        boolQuery.setMustQueries(List.of(
                QueryBuilders.term(Constant.COLUMN_NAME_SESSION_ID, sessionId),
                QueryBuilders.term(Constant.COLUMN_NAME_INDICATOR_TYPE, indicatorType)
        ));

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_TIMESTAMP, SortOrder.ASC)));

        return searchAll(Constant.SEARCH_INDEX_FRAUD_INDICATOR_LOG, boolQuery, sort);
    }

    @Override
    public Boolean insert(FraudIndicatorLog log) {
        if (log.getId() == null) {
            log.setId(idUtils.nextId());
        }
        if (log.getCreated() == null) {
            log.setCreated(System.currentTimeMillis());
        }
        if (log.getTimestamp() == null) {
            log.setTimestamp(System.currentTimeMillis());
        }
        return super.putRow(log);
    }

    @Override
    public Boolean batchInsert(List<FraudIndicatorLog> logs) {
        for (FraudIndicatorLog log : logs) {
            if (log.getId() == null) {
                log.setId(idUtils.nextId());
            }
            if (log.getCreated() == null) {
                log.setCreated(System.currentTimeMillis());
            }
            if (log.getTimestamp() == null) {
                log.setTimestamp(System.currentTimeMillis());
            }
        }
        return super.batchPutRow(logs, RowExistenceExpectation.EXPECT_NOT_EXIST);
    }

    @Override
    public Boolean delete(String id) {
        FraudIndicatorLog entity = new FraudIndicatorLog();
        entity.setId(id);
        return super.deleteRow(entity);
    }

    @Override
    public List<FraudIndicatorLog> getByTimeRange(Long startTime, Long endTime) {
        RangeQuery rangeQuery = new RangeQuery();
        rangeQuery.setFieldName(Constant.COLUMN_NAME_TIMESTAMP);
        rangeQuery.setFrom(startTime, true);
        rangeQuery.setTo(endTime, true);

        Sort sort = new Sort();
        sort.setSorters(List.of(new FieldSort(Constant.COLUMN_NAME_TIMESTAMP, SortOrder.ASC)));

        return searchAll(Constant.SEARCH_INDEX_FRAUD_INDICATOR_LOG, rangeQuery, sort);
    }
}
