package com.drex.core.dal.tablestore.constant;

public class Constant {
    // 表名常量
    public static final String TABLE_NAME_CUSTOMER_REXY_BASKETS = "customer_rexy_baskets";
    public static final String TABLE_NAME_CUSTOMER_REXY = "customer_rexy";
    public static final String TABLE_NAME_REXY_CONFIG = "rexy_config";
    public static final String TABLE_NAME_REXY_BASKET_RECORD = "rexy_basket_record";
    public static final String TABLE_NAME_CUSTOMER_COLLECT_ITEM = "customer_collect_item";
    public static final String TABLE_NAME_INFORMATION = "information";
    public static final String TABLE_NAME_REXY_CLAIM_RECORD = "rexy_claim_record";
    public static final String TABLE_NAME_NOTICE = "notice";

    // YouTube防刷系统表名常量
    public static final String TABLE_NAME_VIDEO_VIEWING_SESSION = "video_viewing_session";
    public static final String TABLE_NAME_FRAUD_INDICATOR_LOG = "fraud_indicator_log";
    public static final String TABLE_NAME_USER_FINGERPRINT = "user_fingerprint";
    public static final String TABLE_NAME_IP_REPUTATION = "ip_reputation";

    // 索引名常量
    public static final String SEARCH_INDEX_SEARCH_LEVEL_INDEX = "search_level_index";
    public static final String SEARCH_INDEX_CUSTOMER_REXY = "search_customer_rexy";
    public static final String SEARCH_INDEX_REXY_BASKET_RECORD = "search_rexy_basket_record";
    public static final String SEARCH_INDEX_INFORMATION_INDEX = "search_information_index";
    public static final String SEARCH_INDEX_NOTICE_INDEX = "search_notice_index";
    public static final String SEARCH_INDEX_REXY_CLAIM_RECORD = "search_rexy_claim_record";

    // YouTube防刷系统索引名常量
    public static final String SEARCH_INDEX_VIDEO_VIEWING_SESSION = "search_video_viewing_session";
    public static final String SEARCH_INDEX_FRAUD_INDICATOR_LOG = "search_fraud_indicator_log";
    public static final String SEARCH_INDEX_USER_FINGERPRINT = "search_user_fingerprint";
    public static final String SEARCH_INDEX_IP_REPUTATION = "search_ip_reputation";

    // 列名常量
    public static final String COLUMN_NAME_CUSTOMER_ID = "customer_id";
    public static final String COLUMN_NAME_REXY_ID = "rexy_id";
    public static final String COLUMN_NAME_REXY_NAME = "rexy_name";
    public static final String COLUMN_NAME_REXY_LEVEL = "rexy_level";
    public static final String COLUMN_NAME_REXY_RATE = "rexy_rate";
    public static final String COLUMN_NAME_REXY_BASKET_LIMIT = "rexy_basket_limit";
    public static final String COLUMN_NAME_CREATED = "created";
    public static final String COLUMN_NAME_ID = "id";
    public static final String COLUMN_NAME_NAME = "name";
    public static final String COLUMN_NAME_LEVEL = "level";
    public static final String COLUMN_NAME_RATE = "rate";
    public static final String COLUMN_NAME_LIMIT = "limit";
    public static final String COLUMN_NAME_EFFECTIVE_TIME = "effective_time";
    public static final String COLUMN_NAME_EXPIRATION_TIME = "expiration_time";
    public static final String COLUMN_NAME_IS_DEFAULT = "is_default";
    public static final String COLUMN_NAME_BASKET_TYPE = "basket_type";
    public static final String COLUMN_NAME_RECEIVED = "received";
    public static final String COLUMN_NAME_BASKET_LIMIT = "basket_limit";
    public static final String COLUMN_NAME_LAST_CLAIM_TIME = "last_claim_time";
    public static final String COLUMN_NAME_BUSINESS_ID = "business_id";
    public static final String COLUMN_NAME_BUSINESS_CODE = "business_code";
    public static final String COLUMN_NAME_OPERATE_TYPE = "operateType";
    public static final String COLUMN_NAME_AMOUNT = "amount";
    public static final String COLUMN_NAME_AVAILABLE = "available";
    public static final String COLUMN_NAME_STATUS = "status";
    public static final String COLUMN_NAME_RESOURCE_ID = "resource_id";
    public static final String COLUMN_NAME_TYPE = "type";
    public static final String COLUMN_NAME_PLATFORM = "platform";
    public static final String COLUMN_NAME_PARAM = "param";
    public static final String COLUMN_NAME_CREATE = "create";

    // Information表列名常量
    public static final String COLUMN_NAME_LOGO = "logo";
    public static final String COLUMN_NAME_TITLE = "title";
    public static final String COLUMN_NAME_SUB_TITLE = "sub_title";
    public static final String COLUMN_NAME_SUMMARY = "summary";
    public static final String COLUMN_NAME_CONTENT = "content";
    public static final String COLUMN_NAME_IMAGE = "image";
    public static final String COLUMN_NAME_LINK = "link";
    public static final String COLUMN_NAME_CATEGORY = "category";
    public static final String COLUMN_NAME_IS_RECOMMEND = "is_recommend";
    public static final String COLUMN_NAME_SORT = "sort";
    public static final String COLUMN_NAME_MODIFIED = "modified";
    public static final String COLUMN_NAME_AVATAR = "avatar";
    public static final String COLUMN_NAME_MINI_AVATAR = "mini_avatar";
    public static final String COLUMN_NAME_REXY_AVATAR = "rexy_avatar";
    public static final String COLUMN_NAME_CIRCLE_AVATAR = "circle_avatar";
    public static final String COLUMN_NAME_TAG = "tag";
    public static final String COLUMN_NAME_DATE = "date";
    public static final String COLUMN_NAME_ORGANIZER = "organizer";
    public static final String COLUMN_NAME_LOCATION = "location";
    public static final String COLUMN_NAME_ACTIVITY_START_TIME = "activity_start_time";
    public static final String COLUMN_NAME_ACTIVITY_END_TIME = "activity_end_time";
    public static final String COLUMN_NAME_POSITION = "position";
    public static final String COLUMN_NAME_IS_REWARD = "is_reward";
    public static final String COLUMN_NAME_REWARD_AMOUNT = "reward_amount";
    public static final String COLUMN_NAME_REWARD_RULES = "reward_rules";

    // 新增列名常量
    public static final String COLUMN_NAME_RECORD_ID = "record_id";
    public static final String COLUMN_NAME_ADDRESS = "address";
    public static final String COLUMN_NAME_POINT = "point";
    public static final String COLUMN_NAME_TRANSFER_HASH_CODE = "transfer_hash_code";

    // Notice表列名常量
    public static final String COLUMN_NAME_NOTIFY_TIME = "notify_time";
    public static final String COLUMN_NAME_SEND_TO = "send_to";

    // YouTube防刷系统列名常量
    // VideoViewingSession表
    public static final String COLUMN_NAME_SESSION_ID = "session_id";
    public static final String COLUMN_NAME_VIDEO_ID = "video_id";
    public static final String COLUMN_NAME_VIDEO_URL = "video_url";
    public static final String COLUMN_NAME_VIDEO_DURATION_SECONDS = "video_duration_seconds";
    public static final String COLUMN_NAME_START_TIME = "start_time";
    public static final String COLUMN_NAME_SERVER_START_TIME = "server_start_time";
    public static final String COLUMN_NAME_END_TIME = "end_time";
    public static final String COLUMN_NAME_SERVER_END_TIME = "server_end_time";
    public static final String COLUMN_NAME_SESSION_STATUS = "session_status";
    public static final String COLUMN_NAME_FINAL_TRUST_SCORE = "final_trust_score";
    public static final String COLUMN_NAME_CLIENT_REPORTED_WATCH_SECONDS = "client_reported_watch_seconds";
    public static final String COLUMN_NAME_CALCULATED_ENGAGED_SECONDS = "calculated_engaged_seconds";
    public static final String COLUMN_NAME_REWARD_GRANTED = "reward_granted";
    public static final String COLUMN_NAME_DENIAL_REASON = "denial_reason";
    public static final String COLUMN_NAME_CLIENT_IP = "client_ip";

    // FraudIndicatorLog表
    public static final String COLUMN_NAME_INDICATOR_TYPE = "indicator_type";
    public static final String COLUMN_NAME_DETAILS = "details";
    public static final String COLUMN_NAME_SCORE_IMPACT = "score_impact";
    public static final String COLUMN_NAME_TIMESTAMP = "timestamp";

    // UserFingerprint表
    public static final String COLUMN_NAME_FINGERPRINT_HASH = "fingerprint_hash";
    public static final String COLUMN_NAME_ATTRIBUTES = "attributes";
    public static final String COLUMN_NAME_IP_ADDRESS = "ip_address";
    public static final String COLUMN_NAME_FIRST_SEEN_AT = "first_seen_at";
    public static final String COLUMN_NAME_LAST_SEEN_AT = "last_seen_at";
    public static final String COLUMN_NAME_ASSOCIATED_USER_IDS = "associated_user_ids";
    public static final String COLUMN_NAME_IS_SUSPICIOUS = "is_suspicious";

    // IpReputation表
    public static final String COLUMN_NAME_REPUTATION_SCORE = "reputation_score";
    public static final String COLUMN_NAME_IS_PROXY = "is_proxy";
    public static final String COLUMN_NAME_IS_VPN = "is_vpn";
    public static final String COLUMN_NAME_IS_TOR_EXIT_NODE = "is_tor_exit_node";
    public static final String COLUMN_NAME_IS_DATA_CENTER = "is_data_center";
    public static final String COLUMN_NAME_COUNTRY_CODE = "country_code";
    public static final String COLUMN_NAME_LAST_CHECKED_AT = "last_checked_at";
    public static final String COLUMN_NAME_SOURCE = "source";
    public static final String COLUMN_NAME_UPDATED_AT = "updated_at";
}
